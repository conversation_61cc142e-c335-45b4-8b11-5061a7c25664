"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/create/page",{

/***/ "(app-pages-browser)/./src/components/ui/date-field.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/date-field.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DateField; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _DateRange__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DateField(param) {\n    let { date = false, handleDateChange, dateID, fieldName = \"Select date\", buttonLabel = \"Set To Now\", hideButton = true } = param;\n    /**\r\n     * Normalise the incoming `date` prop (which may be false, a Dayjs\r\n     * instance, a string, or a Date) into a native Date object that the\r\n     * new DatePicker understands.\r\n     */ const normalisedDate = date && new Date(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).toISOString()).getTime() > 0 ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).toDate() : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full gap-2.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DateRange__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                id: dateID,\n                mode: \"single\",\n                type: \"date\",\n                placeholder: fieldName,\n                value: normalisedDate,\n                /**\r\n                 * DatePicker can return:\r\n                 *   • null                        (when cleared)\r\n                 *   • Date                        (single mode)\r\n                 *   • { startDate, endDate }      (range mode)\r\n                 * We only care about a single Date, so unwrap accordingly.\r\n                 */ onChange: (val)=>{\n                    if (!val) {\n                        handleDateChange(null);\n                    } else if (val instanceof Date) {\n                        handleDateChange(val);\n                    } else if (\"startDate\" in val && val.startDate) {\n                        handleDateChange(val.startDate);\n                    } else if (\"from\" in val && val.from) {\n                        handleDateChange(val.from);\n                    }\n                },\n                clearable: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n                lineNumber: 35,\n                columnNumber: 13\n            }, this),\n            !hideButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: ()=>handleDateChange(new Date()),\n                children: buttonLabel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n                lineNumber: 64,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n        lineNumber: 33,\n        columnNumber: 9\n    }, this);\n}\n_c = DateField;\nvar _c;\n$RefreshReg$(_c, \"DateField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/date-field.tsx\n"));

/***/ })

});