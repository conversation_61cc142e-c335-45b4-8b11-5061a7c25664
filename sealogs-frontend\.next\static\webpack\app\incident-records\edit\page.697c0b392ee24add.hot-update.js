"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/edit/page",{

/***/ "(app-pages-browser)/./src/components/ui/calendar-client.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/calendar-client.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calendar: function() { return /* binding */ Calendar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Calendar,default auto */ \n\n\n// Dynamic import of Calendar component with SSR disabled\nconst Calendar = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_ui_calendar_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\calendar-client.tsx -> \" + \"./calendar\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-4\",\n            children: \"Loading calendar...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\calendar-client.tsx\",\n            lineNumber: 10,\n            columnNumber: 9\n        }, undefined)\n});\n_c1 = Calendar;\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (Calendar);\nvar _c, _c1;\n$RefreshReg$(_c, \"Calendar$dynamic\");\n$RefreshReg$(_c1, \"Calendar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2NhbGVuZGFyLWNsaWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWtDO0FBQ1Q7QUFFekIseURBQXlEO0FBQ3pELE1BQU1FLFdBQVdGLHdEQUFPQSxNQUFDLElBQU0sd05BQU87Ozs7OztJQUNsQ0csS0FBSztJQUNMQyxTQUFTLGtCQUNMLDhEQUFDQztZQUFJQyxXQUFVO3NCQUF1Qzs7Ozs7Ozs7QUFNM0M7QUFDbkIsK0RBQWVKLFFBQVFBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvY2FsZW5kYXItY2xpZW50LnRzeD84ZTRiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbi8vIER5bmFtaWMgaW1wb3J0IG9mIENhbGVuZGFyIGNvbXBvbmVudCB3aXRoIFNTUiBkaXNhYmxlZFxuY29uc3QgQ2FsZW5kYXIgPSBkeW5hbWljKCgpID0+IGltcG9ydCgnLi9jYWxlbmRhcicpLCB7XG4gICAgc3NyOiBmYWxzZSxcbiAgICBsb2FkaW5nOiAoKSA9PiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgICBMb2FkaW5nIGNhbGVuZGFyLi4uXG4gICAgICAgIDwvZGl2PlxuICAgICksXG59KVxuXG5leHBvcnQgeyBDYWxlbmRhciB9XG5leHBvcnQgZGVmYXVsdCBDYWxlbmRhclxuIl0sIm5hbWVzIjpbImR5bmFtaWMiLCJSZWFjdCIsIkNhbGVuZGFyIiwic3NyIiwibG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/calendar-client.tsx\n"));

/***/ })

});