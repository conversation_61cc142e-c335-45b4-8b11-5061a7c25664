hoistPattern:
  - '*'
hoistedDependencies:
  7zip-bin@5.2.0:
    7zip-bin: private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@apollo/cache-control-types@1.0.3(graphql@16.11.0)':
    '@apollo/cache-control-types': private
  '@apollo/protobufjs@1.2.7':
    '@apollo/protobufjs': private
  '@apollo/server-gateway-interface@1.1.1(graphql@16.11.0)':
    '@apollo/server-gateway-interface': private
  '@apollo/server@4.12.2(graphql@16.11.0)':
    '@apollo/server': private
  '@apollo/usage-reporting-protobuf@4.1.1':
    '@apollo/usage-reporting-protobuf': private
  '@apollo/utils.createhash@2.0.2':
    '@apollo/utils.createhash': private
  '@apollo/utils.dropunuseddefinitions@2.0.1(graphql@16.11.0)':
    '@apollo/utils.dropunuseddefinitions': private
  '@apollo/utils.fetcher@2.0.1':
    '@apollo/utils.fetcher': private
  '@apollo/utils.isnodelike@2.0.1':
    '@apollo/utils.isnodelike': private
  '@apollo/utils.keyvaluecache@2.1.1':
    '@apollo/utils.keyvaluecache': private
  '@apollo/utils.logger@2.0.1':
    '@apollo/utils.logger': private
  '@apollo/utils.printwithreducedwhitespace@2.0.1(graphql@16.11.0)':
    '@apollo/utils.printwithreducedwhitespace': private
  '@apollo/utils.removealiases@2.0.1(graphql@16.11.0)':
    '@apollo/utils.removealiases': private
  '@apollo/utils.sortast@2.0.1(graphql@16.11.0)':
    '@apollo/utils.sortast': private
  '@apollo/utils.stripsensitiveliterals@2.0.1(graphql@16.11.0)':
    '@apollo/utils.stripsensitiveliterals': private
  '@apollo/utils.usagereporting@2.1.0(graphql@16.11.0)':
    '@apollo/utils.usagereporting': private
  '@apollo/utils.withrequired@2.0.1':
    '@apollo/utils.withrequired': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.7':
    '@babel/compat-data': private
  '@babel/core@7.27.7':
    '@babel/core': private
  '@babel/generator@7.27.5':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.27.7)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.27.7)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.27.7)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.27.7)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.27.7)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.27.7)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.27.7':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.27.7)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.27.7)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.27.5(@babel/core@7.27.7)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.27.7(@babel/core@7.27.7)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.27.7(@babel/core@7.27.7)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.27.7(@babel/core@7.27.7)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.27.7)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.27.5(@babel/core@7.27.7)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.27.7)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.27.2(@babel/core@7.27.7)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.27.7)':
    '@babel/preset-modules': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.7':
    '@babel/traverse': private
  '@babel/types@7.27.7':
    '@babel/types': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@date-fns/tz@1.2.0':
    '@date-fns/tz': private
  '@develar/schema-utils@2.6.5':
    '@develar/schema-utils': private
  '@electron/asar@3.4.1':
    '@electron/asar': private
  '@electron/get@2.0.3':
    '@electron/get': private
  '@electron/notarize@2.2.1':
    '@electron/notarize': private
  '@electron/osx-sign@1.0.5':
    '@electron/osx-sign': private
  '@electron/universal@1.5.1':
    '@electron/universal': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.3.1':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.10.0':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@graphql-tools/merge@8.4.2(graphql@16.11.0)':
    '@graphql-tools/merge': private
  '@graphql-tools/schema@9.0.19(graphql@16.11.0)':
    '@graphql-tools/schema': private
  '@graphql-tools/utils@9.2.1(graphql@16.11.0)':
    '@graphql-tools/utils': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.11.0)':
    '@graphql-typed-document-node/core': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@hutson/parse-repository-url@3.0.2':
    '@hutson/parse-repository-url': private
  '@ionic/cli-framework-output@2.2.8':
    '@ionic/cli-framework-output': private
  '@ionic/utils-array@2.1.6':
    '@ionic/utils-array': private
  '@ionic/utils-fs@3.1.7':
    '@ionic/utils-fs': private
  '@ionic/utils-object@2.1.5':
    '@ionic/utils-object': private
  '@ionic/utils-process@2.1.10':
    '@ionic/utils-process': private
  '@ionic/utils-stream@3.1.5':
    '@ionic/utils-stream': private
  '@ionic/utils-subprocess@2.1.11':
    '@ionic/utils-subprocess': private
  '@ionic/utils-terminal@2.3.5':
    '@ionic/utils-terminal': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.9':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.7':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.1':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.26':
    '@jridgewell/trace-mapping': private
  '@malept/cross-spawn-promise@1.1.1':
    '@malept/cross-spawn-promise': private
  '@malept/flatpak-bundler@0.4.0':
    '@malept/flatpak-bundler': private
  '@microsoft/fetch-event-source@2.0.1':
    '@microsoft/fetch-event-source': private
  '@mui/base@5.0.0-beta.70(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@mui/base': private
  '@mui/core-downloads-tracker@5.18.0':
    '@mui/core-downloads-tracker': private
  '@mui/material@5.17.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@mui/material': private
  '@mui/private-theming@5.17.1(@types/react@18.3.23)(react@18.3.1)':
    '@mui/private-theming': private
  '@mui/styled-engine@5.18.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(react@18.3.1)':
    '@mui/styled-engine': private
  '@mui/system@5.17.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)':
    '@mui/system': private
  '@mui/types@7.2.24(@types/react@18.3.23)':
    '@mui/types': private
  '@mui/utils@5.17.1(@types/react@18.3.23)(react@18.3.1)':
    '@mui/utils': private
  '@next/env@14.2.30':
    '@next/env': private
  '@next/eslint-plugin-next@14.0.4':
    '@next/eslint-plugin-next': private
  '@next/swc-win32-x64-msvc@14.2.30':
    '@next/swc-win32-x64-msvc': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@playwright/test@1.54.1':
    '@playwright/test': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@prettier/plugin-xml@2.2.0':
    '@prettier/plugin-xml': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@18.3.23)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@18.3.7(@types/react@18.3.23))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@react-leaflet/core@2.1.0(leaflet@1.9.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@react-leaflet/core': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.27.7)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-node-resolve@15.3.1(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@2.79.2)':
    '@rollup/plugin-terser': private
  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    '@rollup/pluginutils': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.5':
    '@swc/helpers': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@tanstack/table-core@8.21.3':
    '@tanstack/table-core': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@trapezedev/gradle-parse@7.1.3':
    '@trapezedev/gradle-parse': private
  '@trapezedev/project@7.1.3(@types/node@20.19.2)(typescript@5.8.3)':
    '@trapezedev/project': private
  '@ts-morph/common@0.12.3':
    '@ts-morph/common': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-axis@3.0.6':
    '@types/d3-axis': private
  '@types/d3-brush@3.0.6':
    '@types/d3-brush': private
  '@types/d3-chord@3.0.6':
    '@types/d3-chord': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-contour@3.0.6':
    '@types/d3-contour': private
  '@types/d3-delaunay@6.0.4':
    '@types/d3-delaunay': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-polygon@3.0.2':
    '@types/d3-polygon': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time-format@4.0.3':
    '@types/d3-time-format': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/d3@7.4.3':
    '@types/d3': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/fs-extra@8.1.5':
    '@types/fs-extra': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/long@4.0.2':
    '@types/long': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/minimist@1.2.5':
    '@types/minimist': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/quill@1.3.10':
    '@types/quill': private
  '@types/raf@3.4.3':
    '@types/raf': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-transition-group@4.4.12(@types/react@18.3.23)':
    '@types/react-transition-group': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/signature_pad@2.3.6':
    '@types/signature_pad': private
  '@types/slice-ansi@4.0.0':
    '@types/slice-ansi': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-win32-x64-msvc@1.9.2':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@userback/widget@0.3.11':
    '@userback/widget': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@wry/caches@1.0.1':
    '@wry/caches': private
  '@wry/context@0.7.4':
    '@wry/context': private
  '@wry/equality@0.5.7':
    '@wry/equality': private
  '@wry/trie@0.5.0':
    '@wry/trie': private
  '@xml-tools/parser@1.0.11':
    '@xml-tools/parser': private
  '@xmldom/xmldom@0.7.13':
    '@xmldom/xmldom': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  '@zeit/schemas@2.36.0':
    '@zeit/schemas': private
  JSONStream@1.3.5:
    JSONStream: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  add-stream@1.0.0:
    add-stream: private
  agent-base@6.0.2:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  app-builder-bin@4.0.0:
    app-builder-bin: private
  app-builder-lib@24.13.3(dmg-builder@24.13.3)(electron-builder-squirrel-windows@24.13.3):
    app-builder-lib: private
  arch@2.2.0:
    arch: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-ify@1.0.0:
    array-ify: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  arrify@1.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  astral-regex@2.0.0:
    astral-regex: private
  async-exit-hook@2.0.1:
    async-exit-hook: private
  async-function@1.0.0:
    async-function: private
  async-retry@1.3.3:
    async-retry: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  b4a@1.6.7:
    b4a: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.27.7):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.27.7):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.27.7):
    babel-plugin-polyfill-regenerator: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.1.5:
    bare-fs: private
  bare-os@3.6.1:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-arraybuffer@1.0.2:
    base64-arraybuffer: private
  base64-js@1.5.1:
    base64-js: private
  big-integer@1.6.52:
    big-integer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  bluebird-lst@1.0.9:
    bluebird-lst: private
  bluebird@3.7.2:
    bluebird: private
  body-parser@1.20.3:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  boolean@3.2.0:
    boolean: private
  boxen@7.0.0:
    boxen: private
  bplist-creator@0.1.0:
    bplist-creator: private
  bplist-parser@0.3.2:
    bplist-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  btoa@1.2.1:
    btoa: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal@1.0.1:
    buffer-equal: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@4.9.2:
    buffer: private
  builder-util-runtime@9.2.4:
    builder-util-runtime: private
  builder-util@24.13.1:
    builder-util: private
  busboy@1.6.0:
    busboy: private
  bytes@3.0.0:
    bytes: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase-keys@6.2.2:
    camelcase-keys: private
  camelcase@7.0.1:
    camelcase: private
  caniuse-lite@1.0.30001726:
    caniuse-lite: private
  canvg@3.0.11:
    canvg: private
  chalk-template@0.4.0:
    chalk-template: private
  chalk@4.1.2:
    chalk: private
  chevrotain@7.1.1:
    chevrotain: private
  chokidar@3.6.0:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  chromium-pickle-js@0.2.0:
    chromium-pickle-js: private
  ci-info@3.9.0:
    ci-info: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-boxes@3.0.0:
    cli-boxes: private
  cli-truncate@2.1.0:
    cli-truncate: private
  client-only@0.0.1:
    client-only: private
  clipboardy@3.0.0:
    clipboardy: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.3:
    clone-response: private
  clone@2.1.2:
    clone: private
  code-block-writer@11.0.3:
    code-block-writer: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@8.3.0:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  compare-func@2.0.0:
    compare-func: private
  compare-version@0.1.2:
    compare-version: private
  compress-commons@4.1.2:
    compress-commons: private
  compressible@2.0.18:
    compressible: private
  compression@1.7.4:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  config-file-ts@0.2.6:
    config-file-ts: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  conventional-changelog-angular@5.0.13:
    conventional-changelog-angular: private
  conventional-changelog-atom@2.0.8:
    conventional-changelog-atom: private
  conventional-changelog-codemirror@2.0.8:
    conventional-changelog-codemirror: private
  conventional-changelog-conventionalcommits@4.6.3:
    conventional-changelog-conventionalcommits: private
  conventional-changelog-core@4.2.4:
    conventional-changelog-core: private
  conventional-changelog-ember@2.0.9:
    conventional-changelog-ember: private
  conventional-changelog-eslint@3.0.9:
    conventional-changelog-eslint: private
  conventional-changelog-express@2.0.6:
    conventional-changelog-express: private
  conventional-changelog-jquery@3.0.11:
    conventional-changelog-jquery: private
  conventional-changelog-jshint@2.0.9:
    conventional-changelog-jshint: private
  conventional-changelog-preset-loader@2.3.4:
    conventional-changelog-preset-loader: private
  conventional-changelog-writer@5.0.1:
    conventional-changelog-writer: private
  conventional-changelog@3.1.25:
    conventional-changelog: private
  conventional-commits-filter@2.0.7:
    conventional-commits-filter: private
  conventional-commits-parser@3.2.4:
    conventional-commits-parser: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  copy-anything@3.0.5:
    copy-anything: private
  core-js-compat@3.43.0:
    core-js-compat: private
  core-js@3.43.0:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-line-break@2.1.0:
    css-line-break: private
  css-select@4.3.0:
    css-select: private
  css-what@6.2.2:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-axis@3.0.0:
    d3-axis: private
  d3-brush@3.0.0:
    d3-brush: private
  d3-chord@3.0.1:
    d3-chord: private
  d3-color@3.1.0:
    d3-color: private
  d3-contour@4.0.2:
    d3-contour: private
  d3-delaunay@6.0.4:
    d3-delaunay: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-polygon@3.0.1:
    d3-polygon: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  d3@7.9.0:
    d3: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  dargs@7.0.0:
    dargs: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  date-fns-jalali@4.1.0-0:
    date-fns-jalali: private
  dateformat@3.0.3:
    dateformat: private
  debug@4.3.4:
    debug: private
  decamelize-keys@1.1.1:
    decamelize-keys: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-equal@1.1.2:
    deep-equal: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  del@6.1.1:
    del: private
  delaunator@5.0.1:
    delaunator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  detect-node@2.1.0:
    detect-node: private
  dezalgo@1.0.4:
    dezalgo: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@5.2.0:
    diff: private
  dir-compare@3.3.0:
    dir-compare: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  dmg-builder@24.13.3(electron-builder-squirrel-windows@24.13.3):
    dmg-builder: private
  doctrine@3.0.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  dompurify@2.5.8:
    dompurify: private
  domutils@2.8.0:
    domutils: private
  dot-prop@5.3.0:
    dot-prop: private
  dotenv-expand@5.1.0:
    dotenv-expand: private
  dotenv@9.0.2:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-builder-squirrel-windows@24.13.3(dmg-builder@24.13.3):
    electron-builder-squirrel-windows: private
  electron-dl@3.5.2:
    electron-dl: private
  electron-is-dev@2.0.0:
    electron-is-dev: private
  electron-publish@24.13.1:
    electron-publish: private
  electron-to-chromium@1.5.177:
    electron-to-chromium: private
  elementtree@0.1.7:
    elementtree: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@2.2.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  err-code@2.0.3:
    err-code: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es6-error@4.1.1:
    es6-error: private
  escalade@3.2.0:
    escalade: private
  escape-goat@2.1.1:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@8.57.1))(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@8.57.1))(eslint@8.57.1))(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@8.57.1))(eslint@8.57.1))(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.0.0-canary-7118f5dd7-20230705(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@1.0.1:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@1.1.1:
    events: private
  execa@5.1.1:
    execa: private
  expand-template@2.0.3:
    expand-template: private
  express@4.21.2:
    express: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  extend@3.0.2:
    extend: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.1.2:
    fast-diff: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.3:
    form-data: private
  formidable@3.5.4:
    formidable: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-pkg-repo@4.2.1:
    get-pkg-repo: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@5.2.0:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  git-raw-commits@2.0.11:
    git-raw-commits: private
  git-remote-origin-url@2.0.0:
    git-remote-origin-url: private
  git-semver-tags@4.1.1:
    git-semver-tags: private
  gitconfiglocal@1.0.0:
    gitconfiglocal: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.1.7:
    glob: private
  global-agent@3.0.0:
    global-agent: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gradle-to-js@2.0.1:
    gradle-to-js: private
  graphemer@1.4.0:
    graphemer: private
  handlebars@4.7.8:
    handlebars: private
  hard-rejection@2.1.0:
    hard-rejection: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ieee754@1.1.13:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@2.0.0:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-ci@3.0.1:
    is-ci: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-port-reachable@4.0.0:
    is-port-reachable: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-text-path@1.0.1:
    is-text-path: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isbinaryfile@5.0.4:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  jmespath@0.16.0:
    jmespath: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonparse@1.3.1:
    jsonparse: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  lazy-val@1.0.5:
    lazy-val: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-json-file@4.0.0:
    load-json-file: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.ismatch@4.4.0:
    lodash.ismatch: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.union@4.6.0:
    lodash.union: private
  loglevel@1.9.2:
    loglevel: private
  long@4.0.0:
    long: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  lru-cache@7.18.3:
    lru-cache: private
  magic-string@0.25.9:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  map-obj@4.3.0:
    map-obj: private
  matcher@3.0.0:
    matcher: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  memoize-one@6.0.0:
    memoize-one: private
  meow@8.1.2:
    meow: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  mergexml@1.2.4:
    mergexml: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@3.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist-options@4.1.0:
    minimist-options: private
  minimist@1.2.8:
    minimist: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mkdirp@1.0.4:
    mkdirp: private
  modify-filename@1.1.0:
    modify-filename: private
  modify-values@1.0.1:
    modify-values: private
  ms@2.1.2:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  napi-postinstall@0.2.5:
    napi-postinstall: private
  native-run@2.0.1:
    native-run: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  node-abi@3.75.0:
    node-abi: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-addon-api@6.1.0:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  node-html-parser@5.4.2:
    node-html-parser: private
  node-releases@2.0.19:
    node-releases: private
  normalize-package-data@3.0.3:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@6.1.0:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@8.4.2:
    open: private
  optimism@0.18.1:
    optimism: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-cancelable@2.1.1:
    p-cancelable: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  p-try@1.0.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parchment@1.1.4:
    parchment: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-is-inside@1.0.2:
    path-is-inside: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pend@1.2.0:
    pend: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  playwright-core@1.54.1:
    playwright-core: private
  playwright@1.54.1:
    playwright: private
  plist@3.1.0:
    plist: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6)(ts-node@10.9.2(@types/node@20.19.2)(typescript@5.8.3)):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  preact@10.12.1:
    preact: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promise-retry@2.0.1:
    promise-retry: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.3:
    pump: private
  punycode@1.3.2:
    punycode: private
  pupa@2.1.1:
    pupa: private
  q@1.5.1:
    q: private
  qs@6.13.0:
    qs: private
  querystring@0.2.0:
    querystring: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  quill-delta@3.6.3:
    quill-delta: private
  quill@1.3.7:
    quill: private
  raf@3.4.1:
    raf: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-is@18.3.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  react-smooth@4.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  read-config-file@6.3.2:
    read-config-file: private
  read-pkg-up@3.0.0:
    read-pkg-up: private
  read-pkg@3.0.0:
    read-pkg: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  redent@3.0.0:
    redent: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  regexp-to-ast@0.5.0:
    regexp-to-ast: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  registry-auth-token@3.3.2:
    registry-auth-token: private
  registry-url@3.1.0:
    registry-url: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  rehackt@0.1.0(@types/react@18.3.23)(react@18.3.1):
    rehackt: private
  replace@1.2.2:
    replace: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-main-filename@2.0.0:
    require-main-filename: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  responselike@2.0.1:
    responselike: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rgbcolor@1.0.1:
    rgbcolor: private
  rimraf@4.4.1:
    rimraf: private
  roarr@2.15.4:
    roarr: private
  robust-predicates@3.0.2:
    robust-predicates: private
  rollup@2.79.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sax@1.2.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  screenfull@5.2.0:
    screenfull: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serialize-error@7.0.1:
    serialize-error: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-handler@6.1.6:
    serve-handler: private
  serve-static@1.16.2:
    serve-static: private
  serve@14.2.4:
    serve: private
  server-only@0.0.1:
    server-only: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  sharp@0.32.6:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  signature_pad@2.3.2:
    signature_pad: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-plist@1.3.1:
    simple-plist: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  smob@1.5.0:
    smob: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.5.7:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spawn-command@0.0.2:
    spawn-command: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  split2@4.2.0:
    split2: private
  split@1.0.1:
    split: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stable-hash@0.0.5:
    stable-hash: private
  stackblur-canvas@2.7.0:
    stackblur-canvas: private
  stat-mode@1.0.0:
    stat-mode: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-buffers@2.2.0:
    stream-buffers: private
  streamsearch@1.1.0:
    streamsearch: private
  streamx@2.22.1:
    streamx: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.1(@babel/core@7.27.7)(react@18.3.1):
    styled-jsx: private
  stylis@4.2.0:
    stylis: private
  sucrase@3.35.0:
    sucrase: private
  sumchecker@3.0.1:
    sumchecker: private
  superjson@2.2.2:
    superjson: private
  supports-color@8.1.1:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-pathdata@6.0.3:
    svg-pathdata: private
  symbol-observable@4.0.0:
    symbol-observable: private
  tapable@2.2.2:
    tapable: private
  tar-fs@3.0.10:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@6.2.1:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  temp-file@3.4.0:
    temp-file: private
  tempy@1.0.1:
    tempy: private
  terser-webpack-plugin@5.3.14(webpack@5.99.9):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  text-decoder@1.2.3:
    text-decoder: private
  text-extensions@1.9.0:
    text-extensions: private
  text-segmentation@1.0.3:
    text-segmentation: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through2@4.0.2:
    through2: private
  through@2.3.8:
    through: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp-promise@3.0.3:
    tmp-promise: private
  tmp@0.2.3:
    tmp: private
  to-buffer@1.2.1:
    to-buffer: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  trim-canvas@0.1.2:
    trim-canvas: private
  trim-newlines@3.0.1:
    trim-newlines: private
  true-myth@4.1.1:
    true-myth: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-invariant@0.10.3:
    ts-invariant: private
  ts-morph@13.0.3:
    ts-morph: private
  ts-node@10.9.2(@types/node@20.19.2)(typescript@5.8.3):
    ts-node: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uglify-js@3.19.3:
    uglify-js: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unique-string@2.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.9.2:
    unrs-resolver: private
  untildify@4.0.0:
    untildify: private
  unused-filename@2.1.0:
    unused-filename: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  update-check@1.5.4:
    update-check: private
  uri-js@4.4.1:
    uri-js: private
  url@0.10.3:
    url: private
  use-callback-ref@1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  use-isomorphic-layout-effect@1.2.1(@types/react@18.3.23)(react@18.3.1):
    use-isomorphic-layout-effect: private
  use-sidecar@1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  utrie@1.0.2:
    utrie: private
  uuid@8.0.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  value-or-promise@1.0.12:
    value-or-promise: private
  vary@1.1.2:
    vary: private
  victory-vendor@36.9.2:
    victory-vendor: private
  watchpack@2.4.4:
    watchpack: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.99.9:
    webpack: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  widest-line@4.0.1:
    widest-line: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  workbox-background-sync@7.1.0:
    workbox-background-sync: private
  workbox-broadcast-update@7.1.0:
    workbox-broadcast-update: private
  workbox-build@7.1.1:
    workbox-build: private
  workbox-cacheable-response@7.1.0:
    workbox-cacheable-response: private
  workbox-core@7.1.0:
    workbox-core: private
  workbox-expiration@7.1.0:
    workbox-expiration: private
  workbox-google-analytics@7.1.0:
    workbox-google-analytics: private
  workbox-navigation-preload@7.1.0:
    workbox-navigation-preload: private
  workbox-precaching@7.1.0:
    workbox-precaching: private
  workbox-range-requests@7.1.0:
    workbox-range-requests: private
  workbox-recipes@7.1.0:
    workbox-recipes: private
  workbox-routing@7.1.0:
    workbox-routing: private
  workbox-strategies@7.1.0:
    workbox-strategies: private
  workbox-streams@7.1.0:
    workbox-streams: private
  workbox-sw@7.1.0:
    workbox-sw: private
  workbox-webpack-plugin@7.1.0(webpack@5.99.9):
    workbox-webpack-plugin: private
  workbox-window@7.1.0:
    workbox-window: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  xcode@3.0.1:
    xcode: private
  xml-js@1.6.11:
    xml-js: private
  xml2js@0.5.0:
    xml2js: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  xpath@0.0.32:
    xpath: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zen-observable-ts@1.2.5:
    zen-observable-ts: private
  zen-observable@0.8.15:
    zen-observable: private
  zip-stream@4.1.1:
    zip-stream: private
ignoredBuilds:
  - sharp
  - electron
  - unrs-resolver
  - '@apollo/protobufjs'
  - aws-sdk
  - core-js
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Tue, 29 Jul 2025 05:22:52 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.3'
  - '@emnapi/runtime@1.4.3'
  - '@emnapi/wasi-threads@1.0.2'
  - '@napi-rs/wasm-runtime@0.2.11'
  - '@next/swc-darwin-arm64@14.2.30'
  - '@next/swc-darwin-x64@14.2.30'
  - '@next/swc-linux-arm64-gnu@14.2.30'
  - '@next/swc-linux-arm64-musl@14.2.30'
  - '@next/swc-linux-x64-gnu@14.2.30'
  - '@next/swc-linux-x64-musl@14.2.30'
  - '@next/swc-win32-arm64-msvc@14.2.30'
  - '@next/swc-win32-ia32-msvc@14.2.30'
  - '@tybys/wasm-util@0.9.0'
  - '@types/plist@3.0.5'
  - '@types/verror@1.10.11'
  - '@unrs/resolver-binding-android-arm-eabi@1.9.2'
  - '@unrs/resolver-binding-android-arm64@1.9.2'
  - '@unrs/resolver-binding-darwin-arm64@1.9.2'
  - '@unrs/resolver-binding-darwin-x64@1.9.2'
  - '@unrs/resolver-binding-freebsd-x64@1.9.2'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.9.2'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.9.2'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-arm64-musl@1.9.2'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.9.2'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-x64-gnu@1.9.2'
  - '@unrs/resolver-binding-linux-x64-musl@1.9.2'
  - '@unrs/resolver-binding-wasm32-wasi@1.9.2'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.9.2'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.9.2'
  - assert-plus@1.0.0
  - core-util-is@1.0.2
  - crc@3.8.0
  - dmg-license@1.0.11
  - extsprintf@1.4.1
  - fsevents@2.3.2
  - fsevents@2.3.3
  - iconv-corefoundation@1.1.7
  - node-addon-api@1.7.2
  - smart-buffer@4.2.0
  - verror@1.10.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
