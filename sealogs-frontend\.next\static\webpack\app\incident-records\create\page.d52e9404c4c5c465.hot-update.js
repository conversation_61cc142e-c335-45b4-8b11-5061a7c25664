"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/incident-records/create/page",{

/***/ "(app-pages-browser)/./src/components/ui/date-field.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/date-field.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DateField; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _DateRange__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DateField(param) {\n    let { date = false, handleDateChange, dateID, fieldName = \"Select date\", buttonLabel = \"Set To Now\", hideButton = true } = param;\n    /**\r\n     * Normalise the incoming `date` prop (which may be false, a Dayjs\r\n     * instance, a string, or a Date) into a native Date object that the\r\n     * new DatePicker understands.\r\n     */ const normalisedDate = date && new Date(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).toISOString()).getTime() > 0 ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).toDate() : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full gap-2.5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DateRange__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                id: dateID,\n                mode: \"single\",\n                type: \"date\",\n                placeholder: fieldName,\n                value: normalisedDate,\n                /**\r\n                 * DatePicker can return:\r\n                 *   • null                        (when cleared)\r\n                 *   • Date                        (single mode)\r\n                 *   • { startDate, endDate }      (range mode)\r\n                 * We only care about a single Date, so unwrap accordingly.\r\n                 */ onChange: (val)=>{\n                    if (!val) {\n                        handleDateChange(null);\n                    } else if (val instanceof Date) {\n                        handleDateChange(val);\n                    } else if (\"startDate\" in val && val.startDate) {\n                        handleDateChange(val.startDate);\n                    } else if (\"from\" in val && val.from) {\n                        handleDateChange(val.from);\n                    }\n                },\n                clearable: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            !hideButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: ()=>handleDateChange(new Date()),\n                children: buttonLabel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n                lineNumber: 65,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\date-field.tsx\",\n        lineNumber: 34,\n        columnNumber: 9\n    }, this);\n}\n_c = DateField;\nvar _c;\n$RefreshReg$(_c, \"DateField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2RhdGUtZmllbGQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV5QjtBQUNBO0FBQ1k7QUFDSjtBQUVsQixTQUFTSSxVQUFVLEtBY2pDO1FBZGlDLEVBQzlCQyxPQUFPLEtBQUssRUFDWkMsZ0JBQWdCLEVBQ2hCQyxNQUFNLEVBQ05DLFlBQVksYUFBYSxFQUN6QkMsY0FBYyxZQUFZLEVBQzFCQyxhQUFhLElBQUksRUFRcEIsR0FkaUM7SUFlOUI7Ozs7S0FJQyxHQUNELE1BQU1DLGlCQUNGTixRQUFRLElBQUlPLEtBQUtYLDRDQUFLQSxDQUFDSSxNQUFNUSxXQUFXLElBQUlDLE9BQU8sS0FBSyxJQUNsRGIsNENBQUtBLENBQUNJLE1BQU1VLE1BQU0sS0FDbEJDO0lBRVYscUJBQ0ksOERBQUNDO1FBQUlDLFdBQVU7OzBCQUVYLDhEQUFDaEIsa0RBQVVBO2dCQUNQaUIsSUFBSVo7Z0JBQ0phLE1BQUs7Z0JBQ0xDLE1BQUs7Z0JBQ0xDLGFBQWFkO2dCQUNiZSxPQUFPWjtnQkFDUDs7Ozs7O2lCQU1DLEdBQ0RhLFVBQVUsQ0FBQ0M7b0JBQ1AsSUFBSSxDQUFDQSxLQUFLO3dCQUNObkIsaUJBQWlCO29CQUNyQixPQUFPLElBQUltQixlQUFlYixNQUFNO3dCQUM1Qk4saUJBQWlCbUI7b0JBQ3JCLE9BQU8sSUFBSSxlQUFlQSxPQUFPQSxJQUFJQyxTQUFTLEVBQUU7d0JBQzVDcEIsaUJBQWlCbUIsSUFBSUMsU0FBUztvQkFDbEMsT0FBTyxJQUFJLFVBQVVELE9BQU9BLElBQUlFLElBQUksRUFBRTt3QkFDbENyQixpQkFBaUJtQixJQUFJRSxJQUFJO29CQUM3QjtnQkFDSjtnQkFDQUMsU0FBUzs7Ozs7O1lBSVosQ0FBQ2xCLDRCQUNFLDhEQUFDUCwyQ0FBTUE7Z0JBQUMwQixTQUFTLElBQU12QixpQkFBaUIsSUFBSU07MEJBQ3ZDSDs7Ozs7Ozs7Ozs7O0FBS3JCO0tBL0R3QkwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvZGF0ZS1maWVsZC50c3g/OGRkZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcclxuaW1wb3J0IGRheWpzIGZyb20gJ2RheWpzJ1xyXG5pbXBvcnQgRGF0ZVBpY2tlciBmcm9tICcuLi9EYXRlUmFuZ2UnXHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vYnV0dG9uJ1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGF0ZUZpZWxkKHtcclxuICAgIGRhdGUgPSBmYWxzZSxcclxuICAgIGhhbmRsZURhdGVDaGFuZ2UsXHJcbiAgICBkYXRlSUQsXHJcbiAgICBmaWVsZE5hbWUgPSAnU2VsZWN0IGRhdGUnLFxyXG4gICAgYnV0dG9uTGFiZWwgPSAnU2V0IFRvIE5vdycsXHJcbiAgICBoaWRlQnV0dG9uID0gdHJ1ZSxcclxufToge1xyXG4gICAgZGF0ZTogYW55XHJcbiAgICBoYW5kbGVEYXRlQ2hhbmdlOiBhbnlcclxuICAgIGRhdGVJRDogYW55XHJcbiAgICBmaWVsZE5hbWU6IGFueVxyXG4gICAgYnV0dG9uTGFiZWw/OiBhbnlcclxuICAgIGhpZGVCdXR0b24/OiBhbnlcclxufSkge1xyXG4gICAgLyoqXHJcbiAgICAgKiBOb3JtYWxpc2UgdGhlIGluY29taW5nIGBkYXRlYCBwcm9wICh3aGljaCBtYXkgYmUgZmFsc2UsIGEgRGF5anNcclxuICAgICAqIGluc3RhbmNlLCBhIHN0cmluZywgb3IgYSBEYXRlKSBpbnRvIGEgbmF0aXZlIERhdGUgb2JqZWN0IHRoYXQgdGhlXHJcbiAgICAgKiBuZXcgRGF0ZVBpY2tlciB1bmRlcnN0YW5kcy5cclxuICAgICAqL1xyXG4gICAgY29uc3Qgbm9ybWFsaXNlZERhdGU6IERhdGUgfCB1bmRlZmluZWQgPVxyXG4gICAgICAgIGRhdGUgJiYgbmV3IERhdGUoZGF5anMoZGF0ZSkudG9JU09TdHJpbmcoKSkuZ2V0VGltZSgpID4gMFxyXG4gICAgICAgICAgICA/IGRheWpzKGRhdGUpLnRvRGF0ZSgpXHJcbiAgICAgICAgICAgIDogdW5kZWZpbmVkXHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgIHsvKiAtLS0gRGF0ZSAvIERhdGUtVGltZSBwaWNrZXIgLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAqL31cclxuICAgICAgICAgICAgPERhdGVQaWNrZXJcclxuICAgICAgICAgICAgICAgIGlkPXtkYXRlSUR9XHJcbiAgICAgICAgICAgICAgICBtb2RlPVwic2luZ2xlXCJcclxuICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtmaWVsZE5hbWV9XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17bm9ybWFsaXNlZERhdGV9XHJcbiAgICAgICAgICAgICAgICAvKipcclxuICAgICAgICAgICAgICAgICAqIERhdGVQaWNrZXIgY2FuIHJldHVybjpcclxuICAgICAgICAgICAgICAgICAqICAg4oCiIG51bGwgICAgICAgICAgICAgICAgICAgICAgICAod2hlbiBjbGVhcmVkKVxyXG4gICAgICAgICAgICAgICAgICogICDigKIgRGF0ZSAgICAgICAgICAgICAgICAgICAgICAgIChzaW5nbGUgbW9kZSlcclxuICAgICAgICAgICAgICAgICAqICAg4oCiIHsgc3RhcnREYXRlLCBlbmREYXRlIH0gICAgICAocmFuZ2UgbW9kZSlcclxuICAgICAgICAgICAgICAgICAqIFdlIG9ubHkgY2FyZSBhYm91dCBhIHNpbmdsZSBEYXRlLCBzbyB1bndyYXAgYWNjb3JkaW5nbHkuXHJcbiAgICAgICAgICAgICAgICAgKi9cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsodmFsKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKCF2YWwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRGF0ZUNoYW5nZShudWxsKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsIGluc3RhbmNlb2YgRGF0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEYXRlQ2hhbmdlKHZhbClcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCdzdGFydERhdGUnIGluIHZhbCAmJiB2YWwuc3RhcnREYXRlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURhdGVDaGFuZ2UodmFsLnN0YXJ0RGF0ZSlcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCdmcm9tJyBpbiB2YWwgJiYgdmFsLmZyb20pIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlRGF0ZUNoYW5nZSh2YWwuZnJvbSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgY2xlYXJhYmxlXHJcbiAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICB7LyogLS0tIOKAnFNldCBUbyBOb3figJ0gcXVpY2stZmlsbCBidXR0b24gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tICovfVxyXG4gICAgICAgICAgICB7IWhpZGVCdXR0b24gJiYgKFxyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEYXRlQ2hhbmdlKG5ldyBEYXRlKCkpfT5cclxuICAgICAgICAgICAgICAgICAgICB7YnV0dG9uTGFiZWx9XHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgIClcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJkYXlqcyIsIkRhdGVQaWNrZXIiLCJCdXR0b24iLCJEYXRlRmllbGQiLCJkYXRlIiwiaGFuZGxlRGF0ZUNoYW5nZSIsImRhdGVJRCIsImZpZWxkTmFtZSIsImJ1dHRvbkxhYmVsIiwiaGlkZUJ1dHRvbiIsIm5vcm1hbGlzZWREYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZ2V0VGltZSIsInRvRGF0ZSIsInVuZGVmaW5lZCIsImRpdiIsImNsYXNzTmFtZSIsImlkIiwibW9kZSIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJ2YWwiLCJzdGFydERhdGUiLCJmcm9tIiwiY2xlYXJhYmxlIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/date-field.tsx\n"));

/***/ })

});